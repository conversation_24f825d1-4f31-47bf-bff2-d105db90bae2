import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

from config import load_config
from database import DatabaseManager
from pulsar_client import PulsarManager

# 全局变量
config = None
db_manager = None
pulsar_manager = None
monitoring_task = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global config, db_manager, pulsar_manager, monitoring_task

    try:
        # 启动时初始化
        config = load_config()

        # 配置日志
        logging.basicConfig(
            level=getattr(logging, config.logging.level),
            format=config.logging.format
        )
        logger = logging.getLogger(__name__)
        logger.info("服务启动中...")

        # 初始化数据库管理器
        db_manager = DatabaseManager(config.database)
        await db_manager.init_pool()

        # 初始化Pulsar管理器
        pulsar_manager = PulsarManager(config.pulsar)
        await pulsar_manager.init_client()

        # 启动监控任务
        monitoring_task = asyncio.create_task(monitor_registrations())

        logger.info("服务启动完成")
        yield

    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        raise
    finally:
        # 关闭时清理资源
        logger.info("服务关闭中...")

        if monitoring_task:
            monitoring_task.cancel()
            try:
                await monitoring_task
            except asyncio.CancelledError:
                pass

        if db_manager:
            await db_manager.close_pool()

        if pulsar_manager:
            await pulsar_manager.close_client()

        logger.info("服务已关闭")


app = FastAPI(
    title="钱包注册监控服务",
    description="监听MySQL钱包注册表并发送消息到Pulsar MQ",
    version="1.0.0",
    lifespan=lifespan
)


async def monitor_registrations():
    """监控钱包注册的后台任务"""
    logger = logging.getLogger(__name__)
    logger.info("开始监控钱包注册...")

    while True:
        try:
            # 获取新的注册记录
            new_registrations = await db_manager.get_new_registrations()

            # 处理每条新记录
            for registration in new_registrations:
                success = await pulsar_manager.send_wallet_registration(registration)
                if success:
                    logger.info(f"处理注册记录成功: ID={registration['id']}")
                else:
                    logger.error(f"处理注册记录失败: ID={registration['id']}")

            # 等待下次轮询
            await asyncio.sleep(config.service.poll_interval)

        except asyncio.CancelledError:
            logger.info("监控任务被取消")
            break
        except Exception as e:
            logger.error(f"监控任务异常: {e}")
            await asyncio.sleep(config.service.poll_interval)


@app.get("/")
async def root():
    """根路径"""
    return {"message": "钱包注册监控服务运行中"}


@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        if not db_manager or not db_manager.pool:
            raise Exception("数据库连接异常")

        # 检查Pulsar连接
        if not pulsar_manager or not pulsar_manager.client:
            raise Exception("Pulsar连接异常")

        return {"status": "healthy", "message": "所有服务正常"}

    except Exception as e:
        raise HTTPException(status_code=503, detail=f"服务异常: {str(e)}")


@app.get("/status")
async def get_status():
    """获取服务状态"""
    return {
        "service": "钱包注册监控服务",
        "last_processed_id": db_manager.last_processed_id if db_manager else 0,
        "poll_interval": config.service.poll_interval if config else 0,
        "database_connected": bool(db_manager and db_manager.pool),
        "pulsar_connected": bool(pulsar_manager and pulsar_manager.client)
    }


@app.post("/manual-sync")
async def manual_sync():
    """手动触发同步"""
    try:
        new_registrations = await db_manager.get_new_registrations()

        success_count = 0
        for registration in new_registrations:
            success = await pulsar_manager.send_wallet_registration(registration)
            if success:
                success_count += 1

        return {
            "message": "手动同步完成",
            "total_records": len(new_registrations),
            "success_count": success_count,
            "failed_count": len(new_registrations) - success_count
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"手动同步失败: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
