# 数据库配置
database:
  host: "localhost"
  port: 3306
  user: "root"
  password: "password"
  database: "your_database"
  charset: "utf8mb4"

# Pulsar配置
pulsar:
  service_url: "pulsar://localhost:6650"
  topic: "wallet-registration"
  producer_name: "wallet-service-producer"

# 服务配置
service:
  host: "0.0.0.0"
  port: 8000
  poll_interval: 5  # 轮询间隔（秒）
  
# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
