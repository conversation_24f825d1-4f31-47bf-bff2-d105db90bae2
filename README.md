# 钱包注册监控服务

这是一个基于FastAPI的Python服务，用于监听MySQL数据库中的钱包注册表，并将新的注册信息发送到Pulsar MQ。

## 功能特性

- 实时监控MySQL数据库中的钱包注册表
- 自动发送新注册信息到Pulsar MQ
- 支持手动触发同步
- 提供健康检查和状态查询接口
- 完整的日志记录和错误处理

## 项目结构

```
.
├── main.py              # 主应用文件
├── config.py            # 配置管理
├── database.py          # 数据库操作
├── pulsar_client.py     # Pulsar客户端
├── config.yaml          # 配置文件
├── requirements.txt     # 依赖包
└── README.md           # 说明文档
```

## 安装和运行

### 1. 创建conda环境

```bash
conda create -n wallet-service python=3.9
conda activate wallet-service
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置文件

修改 `config.yaml` 文件中的数据库和Pulsar连接信息：

```yaml
database:
  host: "your_mysql_host"
  port: 3306
  user: "your_username"
  password: "your_password"
  database: "your_database"

pulsar:
  service_url: "pulsar://your_pulsar_host:6650"
  topic: "wallet-registration"
```

### 4. 数据库表结构

确保MySQL中存在以下表结构：

```sql
CREATE TABLE wallet_registrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_id VARCHAR(255) NOT NULL,
    wallet_address VARCHAR(255) NOT NULL,
    chain_namespace VARCHAR(100),
    chain_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. 运行服务

```bash
python main.py
```

或者使用uvicorn：

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## API接口

### GET /
- 描述：根路径，返回服务状态
- 响应：`{"message": "钱包注册监控服务运行中"}`

### GET /health
- 描述：健康检查
- 响应：服务健康状态

### GET /status
- 描述：获取服务详细状态
- 响应：包含最后处理ID、连接状态等信息

### POST /manual-sync
- 描述：手动触发同步
- 响应：同步结果统计

## 消息格式

发送到Pulsar的消息格式：

```json
{
    "uid": "用户ID",
    "address": "钱包地址",
    "chain": "ERC20",
    "chain_namespace": "链命名空间（可选）",
    "chain_id": "链ID（可选）"
}
```

## 监控机制

- 服务启动时获取当前最大ID作为起始点
- 定期轮询数据库查询新记录（默认5秒间隔）
- 记录处理成功/失败的日志
- 支持服务重启后从上次处理位置继续

## 日志

服务提供详细的日志记录，包括：
- 服务启动/关闭
- 数据库连接状态
- Pulsar连接状态
- 消息发送成功/失败
- 错误异常信息
