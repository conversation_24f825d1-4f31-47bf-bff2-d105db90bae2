import pulsar
import json
import logging
from typing import Dict, Any
from config import PulsarConfig

logger = logging.getLogger(__name__)


class PulsarManager:
    def __init__(self, config: PulsarConfig):
        self.config = config
        self.client = None
        self.producer = None

    async def init_client(self):
        """初始化Pulsar客户端和生产者"""
        try:
            self.client = pulsar.Client(self.config.service_url)
            self.producer = self.client.create_producer(
                topic=self.config.topic,
                producer_name=self.config.producer_name
            )
            logger.info("Pulsar客户端初始化成功")
        except Exception as e:
            logger.error(f"Pulsar客户端初始化失败: {e}")
            raise

    async def send_wallet_registration(self, registration_data: Dict[str, Any]) -> bool:
        """发送钱包注册信息到Pulsar"""
        if not self.producer:
            raise Exception("Pulsar生产者未初始化")
        
        try:
            # 构造消息数据，按照对接方要求的格式
            message_data = {
                "uid": registration_data.get("account_id"),
                "address": registration_data.get("wallet_address"),
                "chain": "ERC20"  # 对接方要求的固定ERC20
            }
            
            # 添加额外信息（可选）
            if registration_data.get("chain_namespace"):
                message_data["chain_namespace"] = registration_data["chain_namespace"]
            if registration_data.get("chain_id"):
                message_data["chain_id"] = registration_data["chain_id"]
            
            # 转换为JSON字符串
            message_json = json.dumps(message_data, ensure_ascii=False)
            
            # 发送消息
            self.producer.send(message_json.encode('utf-8'))
            
            logger.info(f"成功发送钱包注册消息: uid={message_data['uid']}, address={message_data['address']}")
            return True
            
        except Exception as e:
            logger.error(f"发送钱包注册消息失败: {e}")
            return False

    async def close_client(self):
        """关闭Pulsar客户端"""
        try:
            if self.producer:
                self.producer.close()
            if self.client:
                self.client.close()
            logger.info("Pulsar客户端已关闭")
        except Exception as e:
            logger.error(f"关闭Pulsar客户端失败: {e}")
