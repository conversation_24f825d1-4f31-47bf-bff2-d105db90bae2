import yaml
from pydantic import BaseModel
from typing import Optional


class DatabaseConfig(BaseModel):
    host: str
    port: int
    user: str
    password: str
    database: str
    charset: str = "utf8mb4"


class PulsarConfig(BaseModel):
    service_url: str
    topic: str
    producer_name: str


class ServiceConfig(BaseModel):
    host: str = "0.0.0.0"
    port: int = 8000
    poll_interval: int = 5


class LoggingConfig(BaseModel):
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


class Config(BaseModel):
    database: DatabaseConfig
    pulsar: PulsarConfig
    service: ServiceConfig
    logging: LoggingConfig


def load_config(config_path: str = "config.yaml") -> Config:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as file:
        config_data = yaml.safe_load(file)
    return Config(**config_data)
