import aiomysql
import logging
from typing import List, Dict, Any, Optional
from config import DatabaseConfig

logger = logging.getLogger(__name__)


class DatabaseManager:
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.pool: Optional[aiomysql.Pool] = None
        self.last_processed_id = 0

    async def init_pool(self):
        """初始化数据库连接池"""
        try:
            self.pool = await aiomysql.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                db=self.config.database,
                charset=self.config.charset,
                autocommit=True,
                minsize=1,
                maxsize=10
            )
            logger.info("数据库连接池初始化成功")
            
            # 获取当前最大ID作为起始点
            await self._init_last_processed_id()
            
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise

    async def _init_last_processed_id(self):
        """初始化最后处理的ID"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 假设注册表名为 wallet_registrations，主键为 id
                    await cursor.execute("SELECT COALESCE(MAX(id), 0) FROM wallet_registrations")
                    result = await cursor.fetchone()
                    self.last_processed_id = result[0] if result else 0
                    logger.info(f"初始化最后处理ID: {self.last_processed_id}")
        except Exception as e:
            logger.error(f"初始化最后处理ID失败: {e}")
            self.last_processed_id = 0

    async def get_new_registrations(self) -> List[Dict[str, Any]]:
        """获取新的注册记录"""
        if not self.pool:
            raise Exception("数据库连接池未初始化")
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 查询新的注册记录
                    sql = """
                    SELECT id, account_id, wallet_address, chain_namespace, chain_id, created_at
                    FROM wallet_registrations 
                    WHERE id > %s 
                    ORDER BY id ASC
                    """
                    await cursor.execute(sql, (self.last_processed_id,))
                    results = await cursor.fetchall()
                    
                    if results:
                        # 更新最后处理的ID
                        self.last_processed_id = results[-1]['id']
                        logger.info(f"获取到 {len(results)} 条新注册记录，最新ID: {self.last_processed_id}")
                    
                    return results
                    
        except Exception as e:
            logger.error(f"查询新注册记录失败: {e}")
            return []

    async def close_pool(self):
        """关闭数据库连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            logger.info("数据库连接池已关闭")
